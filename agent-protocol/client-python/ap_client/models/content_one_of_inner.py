# coding: utf-8

"""
Agent Protocol

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

The version of the OpenAPI document: 0.1.6
Generated by OpenAPI Generator (https://openapi-generator.tech)

Do not edit the class manually.
"""  # noqa: E501

from __future__ import annotations
import json
import pprint
import re  # noqa: F401
from pydantic import (
    BaseModel,
    ValidationError,
    field_validator,
)
from typing import Optional
from ap_client.models.message_any_block import MessageAnyBlock
from ap_client.models.message_text_block import MessageTextBlock
from typing import Union, Any, Set, TYPE_CHECKING, Dict
from typing_extensions import Self

CONTENTONEOFINNER_ANY_OF_SCHEMAS = ["MessageAnyBlock", "MessageTextBlock"]


class ContentOneOfInner(BaseModel):
    """
    ContentOneOfInner
    """

    # data type: MessageTextBlock
    anyof_schema_1_validator: Optional[MessageTextBlock] = None
    # data type: MessageAnyBlock
    anyof_schema_2_validator: Optional[MessageAnyBlock] = None
    if TYPE_CHECKING:
        actual_instance: Optional[Union[MessageAnyBlock, MessageTextBlock]] = None
    else:
        actual_instance: Any = None
    any_of_schemas: Set[str] = {"MessageAnyBlock", "MessageTextBlock"}

    model_config = {
        "validate_assignment": True,
        "protected_namespaces": (),
    }

    def __init__(self, *args, **kwargs) -> None:
        if args:
            if len(args) > 1:
                raise ValueError(
                    "If a position argument is used, only 1 is allowed to set `actual_instance`"
                )
            if kwargs:
                raise ValueError(
                    "If a position argument is used, keyword arguments cannot be used."
                )
            super().__init__(actual_instance=args[0])
        else:
            super().__init__(**kwargs)

    @field_validator("actual_instance")
    def actual_instance_must_validate_anyof(cls, v):
        ContentOneOfInner.model_construct()
        error_messages = []
        # validate data type: MessageTextBlock
        if not isinstance(v, MessageTextBlock):
            error_messages.append(
                f"Error! Input type `{type(v)}` is not `MessageTextBlock`"
            )
        else:
            return v

        # validate data type: MessageAnyBlock
        if not isinstance(v, MessageAnyBlock):
            error_messages.append(
                f"Error! Input type `{type(v)}` is not `MessageAnyBlock`"
            )
        else:
            return v

        if error_messages:
            # no match
            raise ValueError(
                "No match found when setting the actual_instance in ContentOneOfInner with anyOf schemas: MessageAnyBlock, MessageTextBlock. Details: "
                + ", ".join(error_messages)
            )
        else:
            return v

    @classmethod
    def from_dict(cls, obj: Dict[str, Any]) -> Self:
        return cls.from_json(json.dumps(obj))

    @classmethod
    def from_json(cls, json_str: str) -> Self:
        """Returns the object represented by the json string"""
        instance = cls.model_construct()
        error_messages = []
        # anyof_schema_1_validator: Optional[MessageTextBlock] = None
        try:
            instance.actual_instance = MessageTextBlock.from_json(json_str)
            return instance
        except (ValidationError, ValueError) as e:
            error_messages.append(str(e))
        # anyof_schema_2_validator: Optional[MessageAnyBlock] = None
        try:
            instance.actual_instance = MessageAnyBlock.from_json(json_str)
            return instance
        except (ValidationError, ValueError) as e:
            error_messages.append(str(e))

        if error_messages:
            # no match
            raise ValueError(
                "No match found when deserializing the JSON string into ContentOneOfInner with anyOf schemas: MessageAnyBlock, MessageTextBlock. Details: "
                + ", ".join(error_messages)
            )
        else:
            return instance

    def to_json(self) -> str:
        """Returns the JSON representation of the actual instance"""
        if self.actual_instance is None:
            return "null"

        if hasattr(self.actual_instance, "to_json") and callable(
            self.actual_instance.to_json
        ):
            return self.actual_instance.to_json()
        else:
            return json.dumps(self.actual_instance)

    def to_dict(
        self,
    ) -> Optional[Union[Dict[str, Any], MessageAnyBlock, MessageTextBlock]]:
        """Returns the dict representation of the actual instance"""
        if self.actual_instance is None:
            return None

        if hasattr(self.actual_instance, "to_dict") and callable(
            self.actual_instance.to_dict
        ):
            return self.actual_instance.to_dict()
        else:
            return self.actual_instance

    def to_str(self) -> str:
        """Returns the string representation of the actual instance"""
        return pprint.pformat(self.model_dump())
