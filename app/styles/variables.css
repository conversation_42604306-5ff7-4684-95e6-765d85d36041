/* CSS Variables for HVAC CRM Theme System */

:root {
  /* Base Colors */
  --background: 210 20% 98%; /* Light gray background */
  --foreground: 222.2 84% 4.9%; /* Dark text */

  /* Card Colors */
  --card: 0 0% 100%; /* White cards */
  --card-foreground: 222.2 84% 4.9%; /* Dark text on cards */

  /* Popover Colors */
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;

  /* Primary Colors - A more professional blue */
  --primary: 210 80% 40%; /* Muted blue */
  --primary-foreground: 210 40% 98%; /* Light text on primary */
  --primary-rgb: 51, 102, 153; /* RGB for the new primary blue */

  /* Secondary Colors - Soft neutral gray */
  --secondary: 210 20% 90%; /* Lighter gray */
  --secondary-foreground: 222.2 84% 4.9%; /* Dark text on secondary */

  /* Muted Colors */
  --muted: 210 20% 90%;
  --muted-foreground: 215.4 16.3% 46.9%;

  /* Accent Colors - A slightly softer orange */
  --accent: 30 90% 50%;
  --accent-foreground: 60 9.1% 97.8%;

  /* Destructive Colors - Red */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  /* Success Colors - Green */
  --success: 142.1 76.2% 36.3%;
  --success-foreground: 355.7 100% 97.3%;

  /* Warning Colors - Amber */
  --warning: 32.2 94.6% 43.7%;
  --warning-foreground: 60 9.1% 97.8%;

  /* Info Colors - Cyan */
  --info: 198.6 88.7% 48.4%;
  --info-foreground: 210 40% 98%;

  /* Border and Input */
  --border: 210 20% 80%; /* Lighter border */
  --input: 210 20% 80%; /* Lighter input border */
  --ring: 210 80% 40%; /* Primary color for ring */

  /* Border Radius */
  --radius: 0.5rem;

  /* HVAC Specific Colors */
  --hvac-cooling: 198.6 88.7% 48.4%; /* Cyan for cooling */
  --hvac-heating: 14.3 100% 53.1%; /* Orange-red for heating */
  --hvac-ventilation: 142.1 76.2% 36.3%; /* Green for ventilation */
  --hvac-maintenance: 32.2 94.6% 43.7%; /* Amber for maintenance */
  --hvac-emergency: 0 84.2% 60.2%; /* Red for emergency */

  /* Status Colors */
  --status-active: 142.1 76.2% 36.3%;
  --status-inactive: 215.4 16.3% 46.9%;
  --status-pending: 32.2 94.6% 43.7%;
  --status-completed: 142.1 76.2% 36.3%;
  --status-cancelled: 0 84.2% 60.2%;

  /* Priority Colors */
  --priority-low: 142.1 76.2% 36.3%;
  --priority-medium: 32.2 94.6% 43.7%;
  --priority-high: 14.3 100% 53.1%;
  --priority-urgent: 0 84.2% 60.2%;

  /* Chart Colors */
  --chart-1: 210 80% 40%;
  --chart-2: 30 90% 50%;
  --chart-3: 142.1 76.2% 36.3%;
  --chart-4: 32.2 94.6% 43.7%;
  --chart-5: 198.6 88.7% 48.4%;
}

.dark {
  /* Base Colors */
  --background: 220 20% 15%; /* Dark gray background */
  --foreground: 210 40% 98%; /* Light text */

  /* Card Colors */
  --card: 220 20% 20%; /* Slightly lighter dark gray for cards */
  --card-foreground: 210 40% 98%;

  /* Popover Colors */
  --popover: 220 20% 20%;
  --popover-foreground: 210 40% 98%;

  /* Primary Colors - Adjusted for dark mode */
  --primary: 210 70% 50%;
  --primary-foreground: 220 20% 15%;
  --primary-rgb: 77, 153, 230;

  /* Secondary Colors - Dark Gray */
  --secondary: 220 20% 25%;
  --secondary-foreground: 210 40% 98%;

  /* Muted Colors */
  --muted: 220 20% 25%;
  --muted-foreground: 215 20.2% 65.1%;

  /* Accent Colors - Adjusted for dark mode */
  --accent: 30 90% 55%;
  --accent-foreground: 220 20% 15%;

  /* Destructive Colors - Red (adjusted for dark) */
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  /* Success Colors - Green (adjusted for dark) */
  --success: 142.1 70.6% 45.3%;
  --success-foreground: 220 20% 15%;

  /* Warning Colors - Amber (adjusted for dark) */
  --warning: 32.2 94.6% 43.7%;
  --warning-foreground: 220 20% 15%;

  /* Info Colors - Cyan (adjusted for dark) */
  --info: 198.6 88.7% 48.4%;
  --info-foreground: 220 20% 15%;

  /* Border and Input */
  --border: 220 20% 30%;
  --input: 220 20% 30%;
  --ring: 210 70% 50%;

  /* HVAC Specific Colors (adjusted for dark) */
  --hvac-cooling: 198.6 88.7% 48.4%;
  --hvac-heating: 14.3 100% 53.1%;
  --hvac-ventilation: 142.1 70.6% 45.3%;
  --hvac-maintenance: 32.2 94.6% 43.7%;
  --hvac-emergency: 0 62.8% 30.6%;

  /* Status Colors (adjusted for dark) */
  --status-active: 142.1 70.6% 45.3%;
  --status-inactive: 215 20.2% 65.1%;
  --status-pending: 32.2 94.6% 43.7%;
  --status-completed: 142.1 70.6% 45.3%;
  --status-cancelled: 0 62.8% 30.6%;

  /* Priority Colors (adjusted for dark) */
  --priority-low: 142.1 70.6% 45.3%;
  --priority-medium: 32.2 94.6% 43.7%;
  --priority-high: 14.3 100% 53.1%;
  --priority-urgent: 0 62.8% 30.6%;

  /* Chart Colors (adjusted for dark) */
  --chart-1: 210 70% 50%;
  --chart-2: 30 90% 55%;
  --chart-3: 142.1 70.6% 45.3%;
  --chart-4: 32.2 94.6% 43.7%;
  --chart-5: 198.6 88.7% 48.4%;
}

/* HVAC Specific Utility Classes */
.hvac-cooling {
  color: hsl(var(--hvac-cooling));
}

.hvac-heating {
  color: hsl(var(--hvac-heating));
}

.hvac-ventilation {
  color: hsl(var(--hvac-ventilation));
}

.hvac-maintenance {
  color: hsl(var(--hvac-maintenance));
}

.hvac-emergency {
  color: hsl(var(--hvac-emergency));
}

/* Status Utility Classes */
.status-active {
  color: hsl(var(--status-active));
}

.status-inactive {
  color: hsl(var(--status-inactive));
}

.status-pending {
  color: hsl(var(--status-pending));
}

.status-completed {
  color: hsl(var(--status-completed));
}

.status-cancelled {
  color: hsl(var(--status-cancelled));
}

/* Priority Utility Classes */
.priority-low {
  color: hsl(var(--priority-low));
}

.priority-medium {
  color: hsl(var(--priority-medium));
}

.priority-high {
  color: hsl(var(--priority-high));
}

.priority-urgent {
  color: hsl(var(--priority-urgent));
}

/* Background variants */
.bg-hvac-cooling {
  background-color: hsl(var(--hvac-cooling));
}

.bg-hvac-heating {
  background-color: hsl(var(--hvac-heating));
}

.bg-hvac-ventilation {
  background-color: hsl(var(--hvac-ventilation));
}

.bg-hvac-maintenance {
  background-color: hsl(var(--hvac-maintenance));
}

.bg-hvac-emergency {
  background-color: hsl(var(--hvac-emergency));
}
